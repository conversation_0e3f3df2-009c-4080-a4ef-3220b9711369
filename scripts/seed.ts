import { db } from '../lib/db'
import { zones, clusters, departments, users, userPermissions } from '../lib/db/schema'

async function seed() {
    // Insert users
    await db.insert(users).values([
        { id: '1', name: 'Admin User', email: 'philander<PERSON><EMAIL>' },
        { id: '2', name: 'Zone Minister', email: '<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com' },
    ])

    // Insert zones
    const zonesData = await db.insert(zones).values([
        { zoneName: 'Cluster 1', zoneCode: 'C1', description: 'West Rand and South areas' },
        { zoneName: 'Cluster 2', zoneCode: 'C2', description: 'Ext 9 region areas' },
        { zoneName: 'Cluster 3', zoneCode: 'C3', description: 'Mixed areas' },
        { zoneName: 'Cluster 4', zoneCode: 'C4', description: 'Extended areas' },
    ]).$returningId()

    // Insert clusters
    await db.insert(clusters).values([
        { clusterName: 'West Rand', zoneId: zonesData[0].id },
        { clusterName: 'South', zoneId: zonesData[0].id },
        { clusterName: 'Ext 9', zoneId: zonesData[1].id },
        { clusterName: 'KlipTown', zoneId: zonesData[1].id },
        { clusterName: 'KlipSpruit Ext 1,2', zoneId: zonesData[1].id },
        { clusterName: 'Toekomrus, EnnerDate, Lenasia, Lehea', zoneId: zonesData[1].id },
        // Add more clusters...
    ])

    // Insert departments
    await db.insert(departments).values([
        { deptName: 'SSWATT', deptCode: 'SSWATT' },
        { deptName: 'DnD', deptCode: 'DND' },
        { deptName: 'EOM MENS', deptCode: 'EOM_MENS' },
        { deptName: 'EOM LADIES', deptCode: 'EOM_LADIES' },
        { deptName: 'EOM Security', deptCode: 'EOM_SEC' },
        { deptName: 'EOM Music', deptCode: 'EOM_MUS' },
        { deptName: 'EOM Sound', deptCode: 'EOM_SND' },
        { deptName: 'EOM MEDIA', deptCode: 'EOM_MED' },
        { deptName: 'EOM KIDDIES', deptCode: 'EOM_KID' },
    ])

    console.log('Database seeded successfully!')
}

seed().catch(console.error)