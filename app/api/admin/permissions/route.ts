import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { userPermissions } from "@/lib/db/schema"
import { UserRole } from "@/lib/auth-utils"

export async function POST(request: NextRequest) {
    const session = await auth.api.getSession({
        headers: request.headers
    })

    if (!session || !['super_admin', 'senior_pastor'].includes((session.user as any).role as UserRole)) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        const body = await request.json()
        const { userId, zoneId, clusterId, departmentId, permissionLevel } = body

        const newPermission = await db.insert(userPermissions).values({
            userId,
            zoneId: zoneId || null,
            clusterId: clusterId || null,
            departmentId: departmentId || null,
            permissionLevel,
        }).$returningId

        return NextResponse.json(newPermission[0], { status: 201 })
    } catch (error) {
        console.error("Error assigning permission:", error)
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        )
    }
}