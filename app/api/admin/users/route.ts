import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { users, userPermissions } from "@/lib/db/schema"
import { eq } from "drizzle-orm"
import { UserRole } from "@/lib/auth-utils"

export async function GET(request: NextRequest) {
    const session = await auth.api.getSession({
        headers: request.headers
    })

    if (!session || !['super_admin', 'senior_pastor'].includes((session.user as any).role as UserRole)) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        const usersWithPermissions = await db
            .select({
                id: users.id,
                name: users.name,
                email: users.email,
                role: users.role,
                isActive: users.isActive,
            })
            .from(users)
            .where(eq(users.isActive, true))

        // Fetch permissions for each user
        const usersWithPerms = await Promise.all(
            usersWithPermissions.map(async (user) => {
                const permissions = await db
                    .select()
                    .from(userPermissions)
                    .where(eq(userPermissions.userId, user.id))

                return {
                    ...user,
                    permissions,
                }
            })
        )

        return NextResponse.json(usersWithPerms)
    } catch (error) {
        console.error("Error fetching users:", error)
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        )
    }
}